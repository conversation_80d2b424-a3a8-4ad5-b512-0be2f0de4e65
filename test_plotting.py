import os
import numpy as np
import torch
from PIL import Image
from src.pca import PCA
from src.autoencoder import Autoencoder, DenoisingAutoencoder
import matplotlib.pyplot as plt

# Set data path and random seed
DATA_PATH = './data'
np.random.seed(0)

# Image dimensions
IMG_HEIGHT = 61
IMG_WIDTH = 80

def read_image():
    """Read the subject_05_17.png image"""
    file_path = './data/subject_05_17.png'
    img = Image.open(file_path).convert("L")
    img_array = np.array(img)
    img_vector = img_array.flatten()
    img_vector = img_vector/255.0
    return np.array(img_vector, dtype='float'), img_array.shape

def load_data(split: str) -> tuple[np.ndarray, np.ndarray]:
    """Load training or validation data"""
    data_path = DATA_PATH+'/'+split
    files = os.listdir(data_path)
    image_vectors = []
    label_vectors = []

    for f in files:
        img = Image.open(data_path + '/'+f).convert("L")
        f_name, f_type = os.path.splitext(f)
        label = int(f_name[-2:])-1
        label_vectors.append(label)

        img_array = np.array(img)
        img_vector = img_array.flatten()
        img_vector = img_vector/255.0
        image_vectors.append(img_vector)

    return np.array(image_vectors), np.array(label_vectors)

def reconstruction_loss(img_vec: np.ndarray, img_vec_reconstructed: np.ndarray) -> float:
    """Calculate mean squared error between original and reconstructed image"""
    return ((img_vec - img_vec_reconstructed)**2).mean()

def plot_eigenfaces(pca, save_path='eigenfaces.png'):
    """Plot mean vector and top 4 eigenvectors as images"""
    fig, axes = plt.subplots(1, 5, figsize=(20, 4))
    
    # Plot mean face
    mean_face = pca.mean.reshape(IMG_HEIGHT, IMG_WIDTH)
    axes[0].imshow(mean_face, cmap='gray')
    axes[0].set_title('Mean Face')
    axes[0].axis('off')
    
    # Plot top 4 eigenfaces
    for i in range(4):
        eigenface = pca.components[:, i].reshape(IMG_HEIGHT, IMG_WIDTH)
        axes[i+1].imshow(eigenface, cmap='gray')
        axes[i+1].set_title(f'Eigenface {i+1}')
        axes[i+1].axis('off')
    
    plt.suptitle('Mean Face and Top 4 Eigenfaces', fontsize=16)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"Eigenfaces plot saved as '{save_path}'")

def plot_training_curves(ae_loss_history, dae_loss_history, save_path='training_curves.png'):
    """Plot training curves for both autoencoders"""
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(ae_loss_history)
    plt.title('Autoencoder Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Average Reconstruction Error')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(dae_loss_history)
    plt.title('Denoising Autoencoder Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Average Reconstruction Error')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"Training curves plot saved as '{save_path}'")

def plot_reconstructions(original_img, pca_recon, ae_recon, dae_recon, 
                        pca_mse, ae_mse, dae_mse, save_path='reconstructions.png'):
    """Plot original and reconstructed images side by side"""
    fig, axes = plt.subplots(1, 4, figsize=(20, 5))
    
    # Original image
    axes[0].imshow(original_img.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # PCA reconstruction
    axes[1].imshow(pca_recon.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[1].set_title(f'PCA Reconstruction\nMSE: {pca_mse:.6f}')
    axes[1].axis('off')
    
    # Autoencoder reconstruction
    axes[2].imshow(ae_recon.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[2].set_title(f'Autoencoder Reconstruction\nMSE: {ae_mse:.6f}')
    axes[2].axis('off')
    
    # Denoising Autoencoder reconstruction
    axes[3].imshow(dae_recon.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[3].set_title(f'Denoising Autoencoder Reconstruction\nMSE: {dae_mse:.6f}')
    axes[3].axis('off')
    
    plt.suptitle('Image Reconstruction Comparison', fontsize=16)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"Reconstructions plot saved as '{save_path}'")

def main():
    print("Loading data...")
    X_train, y_train = load_data("train")
    X_val, y_val = load_data("val")
    
    # Use smaller subset for testing
    X_train_small = X_train[:50]  # Use only 50 samples for quick testing
    
    # Train PCA with fewer components
    print("Training PCA...")
    pca = PCA(n_components=10)  # Reduced from 40 to 10
    pca.fit(X_train_small)
    
    # Plot eigenfaces
    print("Plotting eigenfaces...")
    plot_eigenfaces(pca)
    
    # Train Autoencoder with fewer epochs
    print("Training Autoencoder...")
    autoencoder = Autoencoder(input_dim=4880, encoding_dim=100)  # Reduced encoding dim
    ae_loss_history = autoencoder.fit(X_train_small, epochs=50, batch_size=10)  # Reduced epochs
    
    # Train Denoising Autoencoder with fewer epochs
    print("Training Denoising Autoencoder...")
    deno_autoencoder = DenoisingAutoencoder(input_dim=4880, encoding_dim=100)
    dae_loss_history = deno_autoencoder.fit(X_train_small, epochs=50, batch_size=10)
    
    # Plot training curves
    print("Plotting training curves...")
    plot_training_curves(ae_loss_history, dae_loss_history)
    
    # Load test image and reconstruct
    print("Loading test image and reconstructing...")
    img_vec, img_shape = read_image()
    
    # Reconstruct with all methods
    img_reconstruct_pca = pca.reconstruct(img_vec)
    img_reconstruct_ae = autoencoder.reconstruct(torch.tensor(img_vec, dtype=torch.float32))
    img_reconstruct_dae = deno_autoencoder.reconstruct(torch.tensor(img_vec, dtype=torch.float32))
    
    # Calculate MSE for each reconstruction
    mse_pca = reconstruction_loss(img_vec, img_reconstruct_pca)
    mse_ae = reconstruction_loss(img_vec, img_reconstruct_ae)
    mse_dae = reconstruction_loss(img_vec, img_reconstruct_dae)
    
    # Print MSE values
    print(f"\nMean Squared Errors:")
    print(f"PCA: {mse_pca:.6f}")
    print(f"Autoencoder: {mse_ae:.6f}")
    print(f"Denoising Autoencoder: {mse_dae:.6f}")
    
    # Plot reconstructions
    print("Plotting reconstructions...")
    plot_reconstructions(img_vec, img_reconstruct_pca, img_reconstruct_ae, 
                        img_reconstruct_dae, mse_pca, mse_ae, mse_dae)

if __name__ == "__main__":
    main()
