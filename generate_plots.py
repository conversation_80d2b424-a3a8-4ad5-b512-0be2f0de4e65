"""
This script generates the required plots for the homework assignment:
(a) Mean vector and top 4 eigenvectors as eigenfaces
(b) Training curves for Autoencoder and DenoisingAutoencoder
(c) Original and reconstructed images side by side with MSE values

Usage: python generate_plots.py
"""

import os
import numpy as np
import torch
from PIL import Image
from src.pca import PCA
from src.autoencoder import Autoencoder, DenoisingAutoencoder
import matplotlib.pyplot as plt

# Set data path and random seed
DATA_PATH = './data'
np.random.seed(0)

# Image dimensions
IMG_HEIGHT = 61
IMG_WIDTH = 80

def read_image():
    """Read the subject_05_17.png image"""
    file_path = './data/subject_05_17.png'
    img = Image.open(file_path).convert("L")
    img_array = np.array(img)
    img_vector = img_array.flatten()
    img_vector = img_vector/255.0
    return np.array(img_vector, dtype='float'), img_array.shape

def load_data(split: str) -> tuple[np.ndarray, np.ndarray]:
    """Load training or validation data"""
    data_path = DATA_PATH+'/'+split
    files = os.listdir(data_path)
    image_vectors = []
    label_vectors = []

    for f in files:
        img = Image.open(data_path + '/'+f).convert("L")
        f_name, f_type = os.path.splitext(f)
        label = int(f_name[-2:])-1
        label_vectors.append(label)

        img_array = np.array(img)
        img_vector = img_array.flatten()
        img_vector = img_vector/255.0
        image_vectors.append(img_vector)

    return np.array(image_vectors), np.array(label_vectors)

def reconstruction_loss(img_vec: np.ndarray, img_vec_reconstructed: np.ndarray) -> float:
    """Calculate mean squared error between original and reconstructed image"""
    return ((img_vec - img_vec_reconstructed)**2).mean()

def plot_eigenfaces(pca, save_path='eigenfaces.png'):
    """
    Plot mean vector and top 4 eigenvectors as images (eigenfaces)
    Each eigenface represents an informative "face ingredient"
    """
    fig, axes = plt.subplots(1, 5, figsize=(25, 5))
    
    # Plot mean face
    mean_face = pca.mean.reshape(IMG_HEIGHT, IMG_WIDTH)
    im0 = axes[0].imshow(mean_face, cmap='gray')
    axes[0].set_title('Mean Vector\n(Average Face)', fontsize=16, pad=20)
    axes[0].axis('off')
    
    # Plot top 4 eigenfaces
    for i in range(4):
        eigenface = pca.components[:, i].reshape(IMG_HEIGHT, IMG_WIDTH)
        im = axes[i+1].imshow(eigenface, cmap='gray')
        axes[i+1].set_title(f'Eigenface {i+1}\n(Face Ingredient {i+1})', fontsize=16, pad=20)
        axes[i+1].axis('off')
    
    plt.suptitle('Mean Vector and Top 4 Eigenvectors as Eigenfaces', fontsize=20, y=0.95)
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"✓ Eigenfaces plot saved as '{save_path}'")

def plot_training_curves_combined(ae_loss_history, dae_loss_history, save_path='training_curves_combined.png'):
    """Plot training curves for both autoencoders on the same plot"""
    plt.figure(figsize=(12, 8))
    
    plt.plot(ae_loss_history, 'b-', linewidth=2, label='Autoencoder', alpha=0.8)
    plt.plot(dae_loss_history, 'r-', linewidth=2, label='Denoising Autoencoder', alpha=0.8)
    
    plt.title('Training Loss Comparison: Autoencoder vs Denoising Autoencoder', fontsize=16)
    plt.xlabel('Epoch', fontsize=14)
    plt.ylabel('Average Reconstruction Error (MSE)', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"✓ Combined training curves plot saved as '{save_path}'")

def plot_training_curves_separate(ae_loss_history, dae_loss_history, save_path='training_curves_separate.png'):
    """Plot training curves for both autoencoders separately"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # Autoencoder
    ax1.plot(ae_loss_history, 'b-', linewidth=2)
    ax1.set_title('Autoencoder Training Loss', fontsize=16)
    ax1.set_xlabel('Epoch', fontsize=14)
    ax1.set_ylabel('Average Reconstruction Error (MSE)', fontsize=14)
    ax1.grid(True, alpha=0.3)
    
    # Denoising Autoencoder
    ax2.plot(dae_loss_history, 'r-', linewidth=2)
    ax2.set_title('Denoising Autoencoder Training Loss', fontsize=16)
    ax2.set_xlabel('Epoch', fontsize=14)
    ax2.set_ylabel('Average Reconstruction Error (MSE)', fontsize=14)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"✓ Separate training curves plot saved as '{save_path}'")

def plot_reconstructions_large(original_img, pca_recon, ae_recon, dae_recon, 
                              pca_mse, ae_mse, dae_mse, save_path='reconstructions_large.png'):
    """Plot original and reconstructed images side by side as large as possible"""
    fig, axes = plt.subplots(1, 4, figsize=(24, 8))
    
    # Original image
    axes[0].imshow(original_img.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[0].set_title('Original Image\n(subject_05_17.png)', fontsize=18, pad=20)
    axes[0].axis('off')
    
    # PCA reconstruction
    axes[1].imshow(pca_recon.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[1].set_title(f'PCA Reconstruction\n(40 components)\nMSE: {pca_mse:.6f}', fontsize=18, pad=20)
    axes[1].axis('off')
    
    # Autoencoder reconstruction
    axes[2].imshow(ae_recon.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[2].set_title(f'Autoencoder\nReconstruction\nMSE: {ae_mse:.6f}', fontsize=18, pad=20)
    axes[2].axis('off')
    
    # Denoising Autoencoder reconstruction
    axes[3].imshow(dae_recon.reshape(IMG_HEIGHT, IMG_WIDTH), cmap='gray')
    axes[3].set_title(f'Denoising Autoencoder\nReconstruction\nMSE: {dae_mse:.6f}', fontsize=18, pad=20)
    axes[3].axis('off')
    
    plt.suptitle('Image Reconstruction Comparison', fontsize=22, y=0.95)
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"✓ Large reconstructions plot saved as '{save_path}'")

def main():
    print("=" * 60)
    print("GENERATING PLOTS FOR HOMEWORK ASSIGNMENT")
    print("=" * 60)
    
    print("\n1. Loading data...")
    X_train, y_train = load_data("train")
    
    print("2. Training PCA...")
    pca = PCA(n_components=40)
    pca.fit(X_train)
    
    print("3. Generating eigenfaces plot (a)...")
    plot_eigenfaces(pca)
    
    print("4. Training Autoencoder...")
    autoencoder = Autoencoder(input_dim=4880, encoding_dim=488)
    ae_loss_history = autoencoder.fit(X_train, epochs=500, batch_size=135)
    
    print("5. Training Denoising Autoencoder...")
    deno_autoencoder = DenoisingAutoencoder(input_dim=4880, encoding_dim=488)
    dae_loss_history = deno_autoencoder.fit(X_train, epochs=500, batch_size=135)
    
    print("6. Generating training curves plots (b)...")
    plot_training_curves_combined(ae_loss_history, dae_loss_history)
    plot_training_curves_separate(ae_loss_history, dae_loss_history)
    
    print("7. Loading test image and reconstructing...")
    img_vec, img_shape = read_image()
    
    # Reconstruct with all methods
    img_reconstruct_pca = pca.reconstruct(img_vec)
    img_reconstruct_ae = autoencoder.reconstruct(torch.tensor(img_vec, dtype=torch.float32))
    img_reconstruct_dae = deno_autoencoder.reconstruct(torch.tensor(img_vec, dtype=torch.float32))
    
    # Calculate MSE for each reconstruction
    mse_pca = reconstruction_loss(img_vec, img_reconstruct_pca)
    mse_ae = reconstruction_loss(img_vec, img_reconstruct_ae)
    mse_dae = reconstruction_loss(img_vec, img_reconstruct_dae)
    
    print("8. Generating reconstruction comparison plot (c)...")
    plot_reconstructions_large(img_vec, img_reconstruct_pca, img_reconstruct_ae, 
                              img_reconstruct_dae, mse_pca, mse_ae, mse_dae)
    
    print("\n" + "=" * 60)
    print("RESULTS SUMMARY")
    print("=" * 60)
    print(f"Mean Squared Errors for Image Reconstruction:")
    print(f"  • PCA (40 components):        {mse_pca:.6f}")
    print(f"  • Autoencoder:               {mse_ae:.6f}")
    print(f"  • Denoising Autoencoder:     {mse_dae:.6f}")
    print("\nAll plots have been generated and saved!")
    print("Files created:")
    print("  • eigenfaces.png")
    print("  • training_curves_combined.png")
    print("  • training_curves_separate.png")
    print("  • reconstructions_large.png")

if __name__ == "__main__":
    main()
